# 实体类审计基类使用指南

## 概述

为了统一管理实体类中的审计字段（`created_at`、`updated_at`、`created_by`、`updated_by`），我们创建了两个基类：

1. **BaseAuditEntity** - 包含时间戳审计字段
2. **BaseUserAuditEntity** - 包含时间戳和用户审计字段

## 基类说明

### BaseAuditEntity

包含以下字段：
- `createdAt` (LocalDateTime) - 创建时间，自动设置
- `updatedAt` (LocalDateTime) - 更新时间，自动更新

特点：
- 使用 `@CreationTimestamp` 和 `@UpdateTimestamp` 注解自动管理时间戳
- 数据库字段名使用 snake_case (`created_at`, `updated_at`)
- Java 字段名使用 camelCase (`createdAt`, `updatedAt`)
- 时间格式：yyyy-MM-dd HH:mm:ss

### BaseUserAuditEntity

继承自 `BaseAuditEntity`，额外包含：
- `createdBy` (String) - 创建者，需要手动设置
- `updatedBy` (String) - 更新者，需要手动设置

## 使用方法

### 1. 继承 BaseAuditEntity

适用于只需要时间戳审计的实体类：

```java
@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "your_table", indexes = {
        @Index(name = "idx_created_at", columnList = "created_at")
})
public class YourEntity extends BaseAuditEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    // 其他业务字段...
}
```

### 2. 继承 BaseUserAuditEntity

适用于需要完整审计信息的实体类：

```java
@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "your_table", indexes = {
        @Index(name = "idx_created_at", columnList = "created_at")
})
public class YourEntity extends BaseUserAuditEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    // 其他业务字段...
}
```

## 迁移现有实体类

### 步骤 1：更新类声明

```java
// 原来
public class YourEntity {

// 改为
public class YourEntity extends BaseAuditEntity {
// 或
public class YourEntity extends BaseUserAuditEntity {
```

### 步骤 2：添加必要的注解

```java
@Data
@EqualsAndHashCode(callSuper = true)  // 重要：包含父类字段在 equals/hashCode 中
```

### 步骤 3：移除重复的审计字段

删除以下字段定义（因为现在从基类继承）：

```java
// 删除这些字段
@CreationTimestamp
@Column(name = "created_at", updatable = false)
private LocalDateTime createdAt;

@UpdateTimestamp
@Column(name = "updated_at")
private LocalDateTime updatedAt;

@Column(name = "created_by", length = 255)
private String createdBy;

@Column(name = "updated_by", length = 255)
private String updatedBy;
```

### 步骤 4：清理导入

移除不再需要的导入：

```java
// 如果不再使用，可以移除
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
```

### 步骤 5：添加索引（推荐）

在 `@Table` 注解中添加 `created_at` 索引：

```java
@Table(name = "your_table", indexes = {
        // 其他索引...
        @Index(name = "idx_created_at", columnList = "created_at")
})
```

## 注意事项

1. **@EqualsAndHashCode(callSuper = true)**：使用 Lombok 时必须添加此注解，确保父类字段包含在 equals/hashCode 方法中

2. **时间戳自动管理**：`createdAt` 和 `updatedAt` 由 Hibernate 自动管理，无需手动设置

3. **用户字段手动设置**：`createdBy` 和 `updatedBy` 需要在业务逻辑中手动设置

4. **数据库兼容性**：基类使用的字段名和类型与现有数据库架构兼容

5. **测试更新**：迁移后需要更新相关的单元测试，确保时间戳字段的访问正常

## 示例

### 已迁移的实体类

- `Plan` - 继承 `BaseAuditEntity`
- `AlertConfiguration` - 继承 `BaseUserAuditEntity`
- `BriefingConfiguration` - 继承 `BaseAuditEntity`
- `DictionaryItem` - 继承 `BaseAuditEntity`

### 待迁移的实体类

其他包含 `created_at`/`updated_at` 字段的实体类都可以按照上述步骤进行迁移。

## 好处

1. **代码复用**：减少重复的审计字段定义
2. **一致性**：确保所有实体类的审计字段格式一致
3. **维护性**：审计逻辑集中管理，便于后续修改
4. **标准化**：建立统一的实体类设计标准
