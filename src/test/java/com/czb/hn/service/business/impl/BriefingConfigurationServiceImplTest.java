package com.czb.hn.service.business.impl;

import com.czb.hn.dto.briefing.config.ContentSettingsDto;
import com.czb.hn.dto.briefing.BriefingConfigurationCreateDto;
import com.czb.hn.dto.briefing.BriefingConfigurationResponseDto;
import com.czb.hn.dto.briefing.config.BriefingReceptionSettingsDto;
import com.czb.hn.jpa.securadar.entity.BriefingConfiguration;
import com.czb.hn.jpa.securadar.repository.BriefingConfigurationRepository;
import com.czb.hn.service.bulletin.BulletinJobService;
import com.czb.hn.service.business.impl.briefing.BriefingConfigurationServiceImpl;
import com.czb.hn.util.BriefingConfigurationMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for BriefingConfigurationServiceImpl
 */
@ExtendWith(MockitoExtension.class)
public class BriefingConfigurationServiceImplTest {

        @Mock
        private BriefingConfigurationRepository briefingConfigurationRepository;

        @Mock
        private BriefingConfigurationMapper briefingConfigurationMapper;

        @Mock
        private BulletinJobService bulletinJobService;

        @InjectMocks
        private BriefingConfigurationServiceImpl briefingConfigurationService;

        private BriefingConfiguration testConfiguration;
        private BriefingConfigurationCreateDto testCreateDto;
        private BriefingConfigurationResponseDto testResponseDto;

        private ContentSettingsDto validContentSettings;
        private BriefingReceptionSettingsDto validReceptionSettings;

        @BeforeEach
        void setUp() {
                validContentSettings = new ContentSettingsDto(
                                null, // sensitivityType
                                List.of("wb", "wx"), // MediaType
                                List.of(1, 2), // contentTypes
                                0, // MatchMethod
                                null, // isOriginal
                                null,
                                List.of("央级", "省级")// sourceLevel
                );

                BriefingReceptionSettingsDto.EmailRecipientDto emailRecipient = new BriefingReceptionSettingsDto.EmailRecipientDto(
                                "Test User",
                                "<EMAIL>");

                BriefingReceptionSettingsDto.EmailConfigDto emailConfig = new BriefingReceptionSettingsDto.EmailConfigDto(
                                true,
                                List.of(emailRecipient));

                BriefingReceptionSettingsDto.SmsRecipientDto smsRecipient = new BriefingReceptionSettingsDto.SmsRecipientDto(
                                "Test User",
                                "13800138000");

                BriefingReceptionSettingsDto.SmsConfigDto smsConfig = new BriefingReceptionSettingsDto.SmsConfigDto(
                                false,
                                List.of(smsRecipient));
                BriefingReceptionSettingsDto.ReceptionMethodsDto receptionMethods = new BriefingReceptionSettingsDto.ReceptionMethodsDto(
                                emailConfig, smsConfig);
                validReceptionSettings = new BriefingReceptionSettingsDto(
                                "10:00", receptionMethods);
                // Setup test data
                testConfiguration = new BriefingConfiguration();
                testConfiguration.setId(1L);
                testConfiguration.setPlanId(100L);

                testCreateDto = new BriefingConfigurationCreateDto(
                                "华能资本",
                                100L,
                                true,
                                validContentSettings,
                                true,
                                validReceptionSettings,
                                true,
                                validReceptionSettings,
                                true,
                                validReceptionSettings);

                testResponseDto = new BriefingConfigurationResponseDto(
                                1L,
                                "华能资本",
                                100L,
                                true,
                                validContentSettings,
                                true,
                                validReceptionSettings,
                                true,
                                validReceptionSettings,
                                true,
                                validReceptionSettings);
        }

        @Test
        void createConfiguration_Success() {
                // Given
                when(briefingConfigurationRepository.existsByPlanId(100L)).thenReturn(false);
                when(briefingConfigurationMapper.toEntity(any(BriefingConfigurationCreateDto.class)))
                                .thenReturn(testConfiguration);
                when(briefingConfigurationRepository.save(any(BriefingConfiguration.class)))
                                .thenReturn(testConfiguration);
                when(briefingConfigurationMapper.toResponseDto(any(BriefingConfiguration.class)))
                                .thenReturn(testResponseDto);
                doNothing().when(bulletinJobService).createBulletinConfiguration(100L);

                // When
                BriefingConfigurationResponseDto result = briefingConfigurationService
                                .createConfiguration(testCreateDto);

                // Then
                assertNotNull(result);
                assertEquals(100L, result.planId());
                verify(briefingConfigurationRepository).save(any(BriefingConfiguration.class));
        }

        @Test
        void createConfiguration_DuplicatePlanId_ThrowsException() {
                // Given
                when(briefingConfigurationRepository.existsByPlanId(100L)).thenReturn(true);

                // When & Then
                RuntimeException exception = assertThrows(RuntimeException.class,
                                () -> briefingConfigurationService.createConfiguration(testCreateDto));
                assertTrue(exception.getMessage().contains("Failed to create briefing configuration") ||
                                exception.getMessage().contains("Configuration plan ID already exists"));
        }

        @Test
        void getConfigurationsByPlanId_Success() {
                // Given
                when(briefingConfigurationRepository.findByPlanId(100L)).thenReturn(testConfiguration);
                when(briefingConfigurationMapper.toResponseDto(any(BriefingConfiguration.class)))
                                .thenReturn(testResponseDto);

                // When
                BriefingConfigurationResponseDto result = briefingConfigurationService.getConfigurationsByPlanId(100L);

                // Then
                assertNotNull(result);
                assertEquals(100L, result.planId());
        }

        @Test
        void getConfigurationsByPlanId_NotFound_ReturnsNull() {
                // Given
                when(briefingConfigurationRepository.findByPlanId(999L)).thenReturn(null);

                // When
                BriefingConfigurationResponseDto result = briefingConfigurationService.getConfigurationsByPlanId(999L);

                // Then
                assertNull(result);
        }

        @Test
        void isConfigurationPlanIdAvailable_Available_ReturnsTrue() {
                // Given
                when(briefingConfigurationRepository.existsByPlanId(200L)).thenReturn(false);

                // When
                Boolean result = briefingConfigurationService.isConfigurationPlanIdAvailable(200L);

                // Then
                assertTrue(result);
        }

        @Test
        void isConfigurationPlanIdAvailable_NotAvailable_ReturnsFalse() {
                // Given
                when(briefingConfigurationRepository.existsByPlanId(100L)).thenReturn(true);

                // When
                Boolean result = briefingConfigurationService.isConfigurationPlanIdAvailable(100L);

                // Then
                assertFalse(result);
        }
}
