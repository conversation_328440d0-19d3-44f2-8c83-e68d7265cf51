package com.czb.hn.dto.briefing.config;

import io.swagger.v3.oas.annotations.media.Schema;

import javax.annotation.Nullable;
import java.util.List;

public record ContentSettingsDto(
        @Schema(description = "信息属性", example = "[1,2]", allowableValues = {"1", "2", "3"})
        @Nullable
        List<Integer> SensitivityType,

        @Schema(description = "来源类型", example = "[\"wb\", \"wx\"]", allowableValues = {
                "hdlt", "wb", "wx", "zmtapp", "sp", "szb", "wz" })
        List<String> MediaType,

        @Schema(description = "内容包含", example = "[1, 2]", allowableValues = {
                "1", "2", "3", "4" })
        List<Integer> contentTypes,

        @Schema(description = "匹配方式", example = "0", allowableValues = {"0", "1", "2"})
        Integer MatchMethod,

        @Schema(description = "内容类型", example = "1", allowableValues = {"1", "2"})
        @Nullable
        Integer isOriginal,

        @Schema(description = "行业信息")
        @Nullable
        List<String> SecondTrades,

        @Schema(description = "信源级别")
        @Nullable
        List<String> MediaLevel
) {
    public ContentSettingsDto {
        validateListEnumValuesInt(SensitivityType, List.of(1,2,3),
                "SensitivityType");
        validateListEnumValues(MediaType,
                List.of("hdlt", "wb", "wx", "zmtapp", "sp", "szb", "wz"),
                "MediaType");
        validateListEnumValuesInt(contentTypes, List.of(1, 2, 3, 4),
                "contentTypes");
        validateEnumValueInt(MatchMethod, List.of(0,1,2),
                "MatchMethod");
        validateEnumValueInt(isOriginal, List.of(1,2),
                "isOriginal");
        validateListEnumValues(SecondTrades,
                List.of("政务", "公安", "司法", "教育", "能源环保", "房产", "医疗", "金融", "招标", "汽车", "科技",
                        "军事", "游戏", "娱乐", "体育", "母婴", "文化", "时尚", "生活", "彩票", "旅游", "通信", "电商", "其他"),
                "SecondTrades");
        validateListEnumValues(MediaLevel,
                List.of("央级", "省级", "地市", "重点", "中小",
                        "企业商业"),
                "MediaLevel");
    }

    private static void validateEnumValue(String value, List<String> allowedValues, String fieldName) {
        if (value != null && !allowedValues.contains(value)) {
            throw new IllegalArgumentException(fieldName + " must be one of: " + allowedValues);
        }
    }
    private static void validateEnumValueInt(Integer value, List<Integer> allowedValues, String fieldName) {
        if (value != null && !allowedValues.contains(value)) {
            throw new IllegalArgumentException(fieldName + " must be one of: " + allowedValues);
        }
    }

    private static void validateListEnumValues(List<String> values, List<String> allowedValues, String fieldName) {
        if (values != null) {
            for (String value : values) {
                if (value != null && !allowedValues.contains(value)) {
                    throw new IllegalArgumentException(
                            fieldName + " contains invalid value '" + value
                                    + "'. Must be one of: " + allowedValues);
                }
            }
        }
    }
    private static void validateListEnumValuesInt(List<Integer> values, List<Integer> allowedValues, String fieldName) {
        if (values != null) {
            for (Integer value : values) {
                if (value != null && !allowedValues.contains(value)) {
                    throw new IllegalArgumentException(
                            fieldName + " contains invalid value '" + value
                                    + "'. Must be one of: " + allowedValues);
                }
            }
        }
    }
}
