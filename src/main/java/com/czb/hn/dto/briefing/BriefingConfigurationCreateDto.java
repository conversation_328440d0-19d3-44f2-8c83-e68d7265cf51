package com.czb.hn.dto.briefing;

import com.czb.hn.dto.briefing.config.ContentSettingsDto;
import com.czb.hn.dto.briefing.config.BriefingReceptionSettingsDto;
import io.swagger.v3.oas.annotations.media.Schema;

public record BriefingConfigurationCreateDto(
        @Schema(description = "Configuration name", example = "华能资本", requiredMode = Schema.RequiredMode.REQUIRED)
        String name,

        @Schema(description = "Associated plan ID", example = "1")
        Long planId,

        @Schema(description = "Whether the briefing is enabled", example = "true")
        Boolean isActive,

        @Schema(description = "Content filtering settings", requiredMode = Schema.RequiredMode.REQUIRED)
        ContentSettingsDto contentSettings,

        @Schema(description = "Whether the daily briefing is active", example = "true")
        Boolean dailyBriefingIsActive,

        @Schema(description = "Daily briefing reception settings", requiredMode = Schema.RequiredMode.REQUIRED)
        BriefingReceptionSettingsDto dailyReceptionSettings,

        @Schema(description = "Whether the weekly briefing is active", example = "true")
        Boolean weeklyBriefingIsActive,

        @Schema(description = "Weekly briefing reception settings", requiredMode = Schema.RequiredMode.REQUIRED)
        BriefingReceptionSettingsDto weeklyReceptionSettings,

        @Schema(description = "Whether the monthly briefing is active", example = "true")
        Boolean monthlyBriefingIsActive,

        @Schema(description = "Monthly briefing reception settings", requiredMode = Schema.RequiredMode.REQUIRED)
        BriefingReceptionSettingsDto monthlyReceptionSettings
) {
    public BriefingConfigurationCreateDto {
        if (!isActive) {
            throw new IllegalArgumentException("Configuration cannot be disabled when create");
        }
        if (name == null || name.isBlank()) {
            throw new IllegalArgumentException("Configuration name cannot be null or blank");
        }
        if (planId == null){
            throw new IllegalArgumentException("Plan ID cannot be null");
        }
        if (contentSettings == null) {
            throw new IllegalArgumentException("Content settings cannot be null");
        }

        if (!dailyBriefingIsActive && !weeklyBriefingIsActive && !monthlyBriefingIsActive) {
            throw new IllegalArgumentException("At least one briefing type must be active");
        }
        if (dailyBriefingIsActive && dailyReceptionSettings == null) {
            throw new IllegalArgumentException("Daily briefing is active but no reception settings provided");
        }
        if (dailyBriefingIsActive && !dailyReceptionSettings.receptionMethods().email().enabled() && !dailyReceptionSettings.receptionMethods().sms().enabled()) {
            throw new IllegalArgumentException("Daily briefing is active but no valid reception methods provided");
        }
        if (weeklyBriefingIsActive && weeklyReceptionSettings == null) {
            throw new IllegalArgumentException("Weekly briefing is active but no reception settings provided");
        }
        if (weeklyBriefingIsActive && !weeklyReceptionSettings.receptionMethods().email().enabled() && !weeklyReceptionSettings.receptionMethods().sms().enabled()) {
            throw new IllegalArgumentException("Weekly briefing is active but no valid reception methods provided");
        }
        if (monthlyBriefingIsActive && monthlyReceptionSettings == null) {
            throw new IllegalArgumentException("Monthly briefing is active but no reception settings provided");
        }
        if (monthlyBriefingIsActive && !monthlyReceptionSettings.receptionMethods().email().enabled() && !monthlyReceptionSettings.receptionMethods().sms().enabled()) {
            throw new IllegalArgumentException("Monthly briefing is active but no valid reception methods provided");
        }


        if (isActive == null){
            isActive = true;
        }

    }
}
