package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "briefing_configurations", indexes = {
        @Index(name = "idx_plan_id", columnList = "plan_id"),
        @Index(name = "idx_name", columnList = "name"),
        @Index(name = "idx_is_active", columnList = "is_active"),
        @Index(name = "idx_created_at", columnList = "created_at")
})
public class BriefingConfiguration extends BaseAuditEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 255)
    private String name;

    @Column(name = "plan_id")
    private Long planId;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    /**
     * Content filtering settings stored as JSON
     * Includes sensitivity type, source types, content types, etc.
     */
    @Column(name = "content_settings", columnDefinition = "JSON")
    private String contentSettings;

    /**
     * Briefing reception and notification settings stored as JSON
     * Includes email, SMS, timing configurations
     */
    @Column(name = "daily_briefing_is_active", nullable = false)
    private Boolean dailyBriefingIsActive = true;
    @Column(name = "daily_reception_settings", columnDefinition = "JSON")
    private String dailyReceptionSettings;

    @Column(name = "weekly_briefing_is_active", nullable = false)
    private Boolean weeklyBriefingIsActive = true;
    @Column(name = "weekly_reception_settings", columnDefinition = "JSON")
    private String weeklyReceptionSettings;

    @Column(name = "monthly_briefing_is_active", nullable = false)
    private Boolean monthlyBriefingIsActive = true;
    @Column(name = "monthly_reception_settings", columnDefinition = "JSON")
    private String monthlyReceptionSettings;

}
