package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * Base audit entity containing common audit fields
 * Provides created_at and updated_at timestamps with automatic management
 * 
 * This class should be extended by entities that need audit tracking
 * The timestamps are automatically managed by Hibernate annotations
 */
@MappedSuperclass
@Getter
@Setter
public abstract class BaseAuditEntity {

    /**
     * Creation timestamp - automatically set when entity is first persisted
     * Format: yyyy-MM-dd HH:mm:ss
     */
    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    /**
     * Last update timestamp - automatically updated when entity is modified
     * Format: yyyy-MM-dd HH:mm:ss
     */
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
