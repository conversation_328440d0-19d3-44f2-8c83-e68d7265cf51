package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import org.hibernate.annotations.Comment;

@Entity
@Table(name = "t_dictionary_item", indexes = {
        @Index(name = "idx_item_type", columnList = "item_type"),
        @Index(name = "idx_created_at", columnList = "created_at")
}, uniqueConstraints = {
        @UniqueConstraint(name = "idx_item_type_item_value", columnNames = "item_type,item_value")
})
public class DictionaryItem extends BaseAuditEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Comment("字典项类型")
    @Column(name = "item_type", nullable = false, columnDefinition = "varchar(100)")
    private String itemType; // 字典项类型，例如 enterprise_type, organization_type

    @Comment("字典项名称")
    @Column(name = "item_name", nullable = false, columnDefinition = "varchar(100)")
    private String itemName; // 字典项名称，

    @Comment("字典项值")
    @Column(name = "item_value", nullable = false, columnDefinition = "varchar(100)")
    private String itemValue; // 字典项值，如类型代码或标识符

    @Comment("字典项描述")
    @Column(name = "description", columnDefinition = "varchar(100)")
    private String description; // 可选的描述信息

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
