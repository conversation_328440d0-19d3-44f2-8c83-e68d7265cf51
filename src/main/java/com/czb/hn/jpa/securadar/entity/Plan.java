package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "plans", indexes = {
        @Index(name = "idx_enterprise_id", columnList = "enterprise_id"),
        @Index(name = "idx_created_at", columnList = "created_at")
})
public class Plan extends BaseAuditEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String name;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(name = "monitor_keywords", columnDefinition = "TEXT", nullable = false)
    private String monitorKeywords;

    @Column(name = "exclude_keywords", columnDefinition = "TEXT")
    private String excludeKeywords;

    @Column(name = "enterprise_id", nullable = false)
    private String enterpriseId;
}