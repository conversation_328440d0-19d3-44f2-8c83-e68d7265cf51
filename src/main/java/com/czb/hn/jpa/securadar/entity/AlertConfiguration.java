package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Alert Configuration Entity
 * Stores sentiment monitoring alert configurations with JSON-based settings
 * Supports snapshot versioning for audit and rollback purposes
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "alert_configurations", indexes = {
        @Index(name = "idx_plan_id", columnList = "plan_id"),
        @Index(name = "idx_enterprise_id", columnList = "enterprise_id"),
        @Index(name = "idx_enabled", columnList = "enabled"),
        @Index(name = "idx_created_at", columnList = "created_at"),
        @Index(name = "idx_name", columnList = "name")
})
public class AlertConfiguration extends BaseUserAuditEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 255)
    private String name;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(name = "plan_id")
    private Long planId;

    @Column(name = "enterprise_id", nullable = false, length = 255)
    private String enterpriseId;

    @Column(nullable = false)
    private Boolean enabled = true;

    /**
     * Alert keywords configuration stored as JSON
     * Structure: {"keywords": ["keyword1", "keyword2"], "description":
     * "description"}
     */
    @Column(name = "alert_keywords", columnDefinition = "JSON")
    private String alertKeywords;

    /**
     * Content filtering settings stored as JSON
     * Includes sensitivity type, source types, content types, etc.
     */
    @Column(name = "content_settings", columnDefinition = "JSON")
    private String contentSettings;

    /**
     * Alert threshold conditions stored as JSON
     * Includes interaction count, fans count, read count thresholds, etc.
     */
    @Column(name = "threshold_settings", columnDefinition = "JSON")
    private String thresholdSettings;

    /**
     * Alert level classification settings stored as JSON
     * Defines general/medium/severe level criteria
     */
    @Column(name = "level_settings", columnDefinition = "JSON")
    private String levelSettings;

    /**
     * Alert reception and notification settings stored as JSON
     * Includes email, SMS, timing configurations
     */
    @Column(name = "reception_settings", columnDefinition = "JSON")
    private String receptionSettings;

    /**
     * Current version number for snapshot tracking
     */
    @Column(name = "current_version", nullable = false)
    private Integer currentVersion = 1;

    /**
     * Indicates if this configuration is currently active
     */
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    /**
     * Last snapshot creation timestamp
     */
    @Column(name = "last_snapshot_at")
    private LocalDateTime lastSnapshotAt;
}
