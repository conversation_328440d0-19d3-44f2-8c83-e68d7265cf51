package com.czb.hn.jpa.securadar.entity;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;

/**
 * Base user audit entity extending BaseAuditEntity with user tracking
 * Provides created_at, updated_at, created_by, and updated_by fields
 * 
 * This class should be extended by entities that need both timestamp and user audit tracking
 * The timestamps are automatically managed by Hibernate annotations from BaseAuditEntity
 * The user fields need to be manually set by the application
 */
@MappedSuperclass
@Getter
@Setter
public abstract class BaseUserAuditEntity extends BaseAuditEntity {

    /**
     * User who created this entity
     */
    @Column(name = "created_by", length = 255)
    private String createdBy;

    /**
     * User who last updated this entity
     */
    @Column(name = "updated_by", length = 255)
    private String updatedBy;
}
