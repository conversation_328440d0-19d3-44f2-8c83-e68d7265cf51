package com.czb.hn.service.bulletin.impl;

import com.czb.hn.client.PdfClient;
import com.czb.hn.dto.ShareLinkDto;
import com.czb.hn.service.bulletin.BulletinGenerator;
import com.czb.hn.service.share.ShareLinkService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * 简报生成器实现类
 */
@Service
public class BulletinGeneratorImpl implements BulletinGenerator {

    private static final Logger log = LoggerFactory.getLogger(BulletinGeneratorImpl.class);

    private final ShareLinkService shareLinkService;
    private final PdfClient pdfClient;

    @Value("${bulletin.briefing.base-url:http://localhost:8080}")
    private String frontendBaseUrl;

    public BulletinGeneratorImpl(ShareLinkService shareLinkService, PdfClient pdfClient) {
        this.shareLinkService = shareLinkService;
        this.pdfClient = pdfClient;
    }

    @Override
    public byte[] generateBulletin(String bulletinType, LocalDate bulletinDate, DateRange dateRange, Long generationId) {
        try {
            log.info("开始生成简报，类型: {}, 日期: {}, 数据范围: {} 至 {}",
                    bulletinType, bulletinDate, dateRange.startDate(), dateRange.endDate());
            
            // 2. 选择模板并生成页面内容URL，使用generationId作为参数
            String htmlPageUrl = buildHtmlPageUrl(generationId);

            // 3. 创建分享链接（有效期1小时，创建人ID可根据业务传入）
            ShareLinkDto shareLink = shareLinkService.createShareLink(htmlPageUrl, 60 * 60 * 1000L, "system");

            // 4. 拼接分享链接URL
            String shareUrl = frontendBaseUrl + "/share/bulletin/" + generationId + "?shareCode=" + shareLink.shareCode();

            // 5. 调用PdfClient生成PDF
            byte[] pdfBytes = pdfClient.generatePdf(shareUrl, shareLink.shareCode());

            return pdfBytes;

        } catch (Exception e) {
            log.error("生成简报PDF异常，类型: {}, 日期: {}, 错误: {}", 
            bulletinType, bulletinDate, e.getMessage(), e);
            throw new RuntimeException("生成简报PDF失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建用于生成简报的HTML页面URL
     */
    private String buildHtmlPageUrl(Long generationId) {
        // 使用可配置的前端基础URL，加上generationId参数
        return String.format("%s/bulletin-detail?generationId=%d", frontendBaseUrl, generationId);
    }
}