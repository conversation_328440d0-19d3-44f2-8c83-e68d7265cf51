package com.czb.hn.service.business;

import com.czb.hn.dto.response.briefing.*;
import com.czb.hn.dto.response.search.SinaNewsSearchResponseDto;

import java.util.List;

/**
 * 新浪舆情Elasticsearch聚合查询服务接口
 */
public interface ElasticsearchBriefingService {

    /**
     * 获取敏感信息趋势图
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param planId 监测方案
     * @param sensitivityType 信息属性（传入一种属性，获取对应的趋势图）
     * @param mediaTypes 媒体来源
     * @param contentTypes 内容包含
     * @return 时间间隔及其对应的文档数量（封装为TimeCountDto列表）
     */
    HistogramSummeryDto getSensitiveInfoTrend(
            String startTime,
            String endTime,
            Long planId,
            List<Integer> sensitivityType,
            List<String> mediaTypes,
            List<Integer> contentTypes,
            Integer isOriginal,
            List<String> SecondTrades,
            List<String> MediaLevel);


    List<MediaDistributionDto> MediaDistribution(
            String startTime,
            String endTime,
            Long planId,
            List<Integer> sensitivityType,
            List<String> mediaTypes,
            List<Integer> contentTypes,
            Integer isOriginal,
            List<String> SecondTrades,
            List<String> MediaLevel);

    List<MediaTierDto> MediaDetail(
            String startTime,
            String endTime,
            Long planId,
            List<Integer> sensitivityType,
            List<String> mediaTypes,
            List<Integer> contentTypes,
            Integer isOriginal,
            List<String> SecondTrades,
            List<String> MediaLevel);

    List<HighFrequencyWordDto> HighFrequencyWords(
            String startTime,
            String endTime,
            Long planId,
            List<Integer> sensitivityType,
            List<String> mediaTypes,
            List<Integer> contentTypes,
            Integer isOriginal,
            List<String> SecondTrades,
            List<String> MediaLevel);

    List<EmotionDistributionDto> EmotionDistribution(
            String startTime,
            String endTime,
            Long planId,
            List<Integer> sensitivityType,
            List<String> mediaTypes,
            List<Integer> contentTypes,
            Integer isOriginal,
            List<String> SecondTrades,
            List<String> MediaLevel);

    List<SinaNewsSearchResponseDto> sensitiveInfoSummary(
            String startTime,
            String endTime,
            Long planId,
            List<Integer> sensitivityType,
            List<String> mediaTypes,
            List<Integer> contentTypes,
            Integer isOriginal,
            List<String> SecondTrades,
            List<String> MediaLevel);
}