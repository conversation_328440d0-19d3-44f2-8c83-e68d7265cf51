package com.czb.hn.web.controllers;

import com.czb.hn.dto.PlanCreateDTO;
import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.PlanUpdateDTO;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.service.business.PlanService;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/plans")
@Tag(name = "PlanController", description = "提供方案管理相关的API，包括创建、查询、更新和删除舆情监控方案")
public class PlanController {

    @Autowired
    private PlanService planService;

    @PostMapping
    @Operation(summary = "创建方案", description = "创建一个新的舆情监控方案，支持关键词逻辑运算符（+表示AND，|表示OR，@@转义+字符）")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "方案创建成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = PlanDTO.class), examples = @ExampleObject(name = "创建成功示例", value = """
                    {
                        "result": "SUCCESS",
                        "message": "Plan created successfully",
                        "data": {
                            "id": 1,
                            "name": "华能资本舆情监控方案",
                            "description": "针对华能资本的全面舆情监控方案",
                            "monitorKeywords": "华能资本+投资|华能集团+金融|新能源+华能",
                            "excludeKeywords": "广告|招聘|测试",
                            "enterprise": {
                                "id": "enterprise123",
                                "name": "华能资本服务有限公司"
                            },
                            "createdAt": "2024-01-15 10:30:00",
                            "updatedAt": "2024-01-15 10:30:00"
                        }
                    }
                    """))),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误", content = @Content(mediaType = "application/json", schema = @Schema(implementation = PlanDTO.class), examples = @ExampleObject(name = "参数错误示例", value = """
                    {
                        "result": "ERROR",
                        "message": "Monitor keywords validation failed: 关键词不能为空",
                        "data": null
                    }
                    """)))
    })
    public ResponseEntity<ApiResponse<PlanDTO>> createPlan(
            @Parameter(description = "方案创建信息", required = true, content = @Content(mediaType = "application/json", schema = @Schema(implementation = PlanCreateDTO.class), examples = @ExampleObject(name = "创建方案示例", value = """
                    {
                        "name": "华能资本舆情监控方案",
                        "description": "针对华能资本的全面舆情监控方案，包括投资动态、市场表现等",
                        "monitorKeywords": "华能资本+投资|华能集团+金融|新能源+华能",
                        "excludeKeywords": "广告|招聘|测试",
                        "enterpriseId": "enterprise123"
                    }
                    """))) @RequestBody PlanCreateDTO planCreateDTO) {
        try {
            PlanDTO createdPlan = planService.createPlan(planCreateDTO);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(new ApiResponse<>("SUCCESS", "Plan created successfully", createdPlan));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>("ERROR", e.getMessage(), null));
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取方案详情", description = "根据方案ID获取方案的详细信息，包括关键词配置和企业信息")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功获取方案详情", content = @Content(mediaType = "application/json", schema = @Schema(implementation = com.czb.hn.dto.response.ApiResponse.class), examples = @ExampleObject(name = "获取成功示例", value = """
                    {
                        "result": "SUCCESS",
                        "message": "操作成功",
                        "data": {
                            "id": 1,
                            "name": "华能资本舆情监控方案",
                            "description": "针对华能资本的全面舆情监控方案",
                            "monitorKeywords": "华能资本+投资|华能集团+金融|新能源+华能",
                            "excludeKeywords": "广告|招聘|测试",
                            "enterprise": {
                                "id": "enterprise123",
                                "name": "华能资本服务有限公司"
                            },
                            "createdAt": "2024-01-15 10:30:00",
                            "updatedAt": "2024-01-15 10:30:00"
                        }
                    }
                    """))),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "方案不存在", content = @Content(mediaType = "application/json", schema = @Schema(implementation = PlanDTO.class), examples = @ExampleObject(name = "方案不存在示例", value = """
                    {
                        "result": "ERROR",
                        "message": "Plan not found with ID: 999",
                        "data": null
                    }
                    """)))
    })
    public ResponseEntity<ApiResponse<PlanDTO>> getPlanById(
            @Parameter(description = "方案ID", required = true, example = "1", schema = @Schema(type = "integer", format = "int64", minimum = "1")) @PathVariable Long id) {
        try {
            PlanDTO plan = planService.getPlanById(id);
            return ResponseEntity.ok(ApiResponse.success(plan));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>("ERROR", e.getMessage(), null));
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新方案", description = "根据方案ID更新方案信息，支持部分字段更新，所有字段均为可选")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "方案更新成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = PlanDTO.class), examples = @ExampleObject(name = "更新成功示例", value = """
                    {
                        "result": "SUCCESS",
                        "message": "操作成功",
                        "data": {
                            "id": 1,
                            "name": "华能资本舆情监控方案（已更新）",
                            "description": "更新后的方案描述",
                            "monitorKeywords": "华能资本+投资|华能集团+金融",
                            "excludeKeywords": "广告|招聘",
                            "enterprise": {
                                "id": "enterprise123",
                                "name": "华能资本服务有限公司"
                            },
                            "createdAt": "2024-01-15 10:30:00",
                            "updatedAt": "2024-01-15 14:20:00"
                        }
                    }
                    """))),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "更新失败", content = @Content(mediaType = "application/json", schema = @Schema(implementation = PlanDTO.class), examples = @ExampleObject(name = "更新失败示例", value = """
                    {
                        "result": "ERROR",
                        "message": "Plan not found with ID: 999",
                        "data": null
                    }
                    """)))
    })
    public ResponseEntity<ApiResponse<PlanDTO>> updatePlan(
            @Parameter(description = "方案ID", required = true, example = "1", schema = @Schema(type = "integer", format = "int64", minimum = "1")) @PathVariable Long id,
            @Parameter(description = "方案更新信息（所有字段均为可选）", required = true, content = @Content(mediaType = "application/json", schema = @Schema(implementation = PlanUpdateDTO.class), examples = @ExampleObject(name = "更新方案示例", value = """
                    {
                        "name": "华能资本舆情监控方案（已更新）",
                        "description": "更新后的方案描述，增加了更多监控维度",
                        "monitorKeywords": "华能资本+投资|华能集团+金融",
                        "excludeKeywords": "广告|招聘"
                    }
                    """))) @RequestBody PlanUpdateDTO planUpdateDTO) {
        try {
            PlanDTO updatedPlan = planService.updatePlan(id, planUpdateDTO);
            return ResponseEntity.ok(ApiResponse.success(updatedPlan));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>("ERROR", e.getMessage(), null));
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除方案", description = "根据方案ID删除方案，删除后无法恢复，请谨慎操作")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "方案删除成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = void.class), examples = @ExampleObject(name = "删除成功示例", value = """
                    {
                        "result": "SUCCESS",
                        "message": "操作成功",
                        "data": null
                    }
                    """))),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "删除失败", content = @Content(mediaType = "application/json", schema = @Schema(implementation = void.class), examples = @ExampleObject(name = "删除失败示例", value = """
                    {
                        "result": "ERROR",
                        "message": "Plan not found with ID: 999",
                        "data": null
                    }
                    """)))
    })
    public ResponseEntity<ApiResponse<Void>> deletePlan(
            @Parameter(description = "方案ID", required = true, example = "1", schema = @Schema(type = "integer", format = "int64", minimum = "1")) @PathVariable Long id) {
        try {
            planService.deletePlan(id);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>("ERROR", e.getMessage(), null));
        }
    }

    @GetMapping("/enterprise/{enterpriseId}")
    @Operation(summary = "根据企业ID获取方案", description = "根据企业ID获取该企业下的所有舆情监控方案列表")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功获取企业方案列表", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = PlanDTO.class)), examples = @ExampleObject(name = "获取成功示例", value = """
                    {
                        "result": "SUCCESS",
                        "message": "操作成功",
                        "data": [
                            {
                                "id": 1,
                                "name": "华能资本舆情监控方案",
                                "description": "针对华能资本的全面舆情监控方案",
                                "monitorKeywords": "华能资本+投资|华能集团+金融",
                                "excludeKeywords": "广告|招聘",
                                "enterprise": {
                                    "id": "enterprise123",
                                    "name": "华能资本服务有限公司"
                                },
                                "createdAt": "2024-01-15 10:30:00",
                                "updatedAt": "2024-01-15 10:30:00"
                            },
                            {
                                "id": 2,
                                "name": "华能新能源监控方案",
                                "description": "专注于新能源业务的舆情监控",
                                "monitorKeywords": "华能+新能源|风电+华能|太阳能+华能",
                                "excludeKeywords": "广告|测试",
                                "enterprise": {
                                    "id": "enterprise123",
                                    "name": "华能资本服务有限公司"
                                },
                                "createdAt": "2024-01-16 09:15:00",
                                "updatedAt": "2024-01-16 09:15:00"
                            }
                        ]
                    }
                    """))),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "获取失败", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = PlanDTO.class)), examples = @ExampleObject(name = "获取失败示例", value = """
                    {
                        "result": "ERROR",
                        "message": "Invalid enterprise ID",
                        "data": null
                    }
                    """)))
    })

    public  ResponseEntity<ApiResponse<List<PlanDTO>>> getPlansByEnterpriseId(
            @Parameter(description = "企业ID", required = true, example = "enterprise123", schema = @Schema(type = "string", minLength = 1, maxLength = 255)) @PathVariable String enterpriseId) {
        try {
            List<PlanDTO> plans = planService.getPlansByEnterpriseId(enterpriseId);
            return ResponseEntity.ok(ApiResponse.success(plans));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>("ERROR", e.getMessage(), null));
        }
    }
}